import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/app_colors.dart';

class ImmersiveTextViewer extends StatefulWidget {
  final String title;
  final String content;
  final String? source;
  final String? fadl;
  final bool isZikr;

  const ImmersiveTextViewer({
    Key? key,
    required this.title,
    required this.content,
    this.source,
    this.fadl,
    this.isZikr = true,
  }) : super(key: key);

  @override
  State<ImmersiveTextViewer> createState() => _ImmersiveTextViewerState();
}

class _ImmersiveTextViewerState extends State<ImmersiveTextViewer>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  double _fontSize = 18.0;
  final double _minFontSize = 12.0;
  final double _maxFontSize = 32.0;
  bool _showControls = true;
  bool _isFullscreen = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadSavedFontSize();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack,
    ));

    _fadeController.forward();
    _scaleController.forward();
  }

  Future<void> _loadSavedFontSize() async {
    // يمكن إضافة حفظ واستعادة حجم الخط المفضل هنا
  }

  void _increaseFontSize() {
    setState(() {
      if (_fontSize < _maxFontSize) {
        _fontSize += 2.0;
        _triggerFontChangeAnimation();
      }
    });
    HapticFeedback.lightImpact();
  }

  void _decreaseFontSize() {
    setState(() {
      if (_fontSize > _minFontSize) {
        _fontSize -= 2.0;
        _triggerFontChangeAnimation();
      }
    });
    HapticFeedback.lightImpact();
  }

  void _resetFontSize() {
    setState(() {
      _fontSize = 18.0;
      _triggerFontChangeAnimation();
    });
    HapticFeedback.mediumImpact();
  }

  void _triggerFontChangeAnimation() {
    _scaleController.reset();
    _scaleController.forward();
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
    HapticFeedback.mediumImpact();
  }

  String _getFormattedContent() {
    String result = '';

    // إضافة العنوان
    result += widget.title;
    result += '\n\n';

    // إضافة المحتوى
    result += widget.content;

    // إضافة المصدر إذا كان متوفراً
    if (widget.source != null && widget.source!.isNotEmpty) {
      result += '\n\nالمصدر: ${widget.source}';
    }

    // إضافة الفضل إذا كان متوفراً
    if (widget.fadl != null && widget.fadl!.isNotEmpty) {
      result += '\n\nالفضل: ${widget.fadl}';
    }

    // إضافة اسم التطبيق
    result += '\n\nمشاركة من تطبيق وهج السالك';

    return result;
  }

  void _copyToClipboard() async {
    await Clipboard.setData(ClipboardData(text: _getFormattedContent()));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم نسخ النص بنجاح'),
          backgroundColor: AppColors.getAzkarColor(
            Theme.of(context).brightness == Brightness.dark,
          ),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
    HapticFeedback.mediumImpact();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : Colors.white,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Stack(
              children: [
                // المحتوى الرئيسي
                GestureDetector(
                  onTap: _toggleControls,
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: _isFullscreen ? 16.0 : 24.0,
                      vertical: _isFullscreen ? 16.0 : 32.0,
                    ),
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // مساحة للأزرار العلوية
                          if (!_isFullscreen) const SizedBox(height: 60),

                          // العنوان
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            child: Text(
                              widget.title,
                              style: TextStyle(
                                fontSize: _fontSize + 4,
                                fontWeight: FontWeight.bold,
                                color: AppColors.getAzkarColor(isDarkMode),
                                height: 1.6,
                              ),
                              textDirection: TextDirection.rtl,
                              textAlign: TextAlign.right,
                            ),
                          ),

                          const SizedBox(height: 24),

                          // المحتوى الرئيسي
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            child: Text(
                              widget.content,
                              style: TextStyle(
                                fontSize: _fontSize,
                                height: 1.8,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                                letterSpacing: 0.5,
                              ),
                              textDirection: TextDirection.rtl,
                              textAlign: TextAlign.right,
                            ),
                          ),

                          // المصدر
                          if (widget.source != null &&
                              widget.source!.isNotEmpty) ...[
                            const SizedBox(height: 32),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.getAzkarColor(isDarkMode)
                                      .withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'المصدر: ${widget.source}',
                                style: TextStyle(
                                  fontSize: _fontSize - 2,
                                  fontStyle: FontStyle.italic,
                                  color: AppColors.getAzkarColor(isDarkMode),
                                  height: 1.6,
                                ),
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],

                          // الفضل
                          if (widget.fadl != null &&
                              widget.fadl!.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.amber.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.amber.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                'الفضل: ${widget.fadl}',
                                style: TextStyle(
                                  fontSize: _fontSize - 2,
                                  color: Colors.amber.shade700,
                                  height: 1.6,
                                ),
                                textDirection: TextDirection.rtl,
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],

                          // مساحة إضافية في الأسفل
                          const SizedBox(height: 100),
                        ],
                      ),
                    ),
                  ),
                ),

                // أزرار التحكم العلوية
                if (_showControls && !_isFullscreen)
                  Positioned(
                    top: 16,
                    left: 16,
                    right: 16,
                    child: AnimatedOpacity(
                      opacity: _showControls ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // زر الإغلاق
                          _buildControlButton(
                            icon: Icons.close,
                            onTap: () => Navigator.of(context).pop(),
                            backgroundColor: Colors.red.withOpacity(0.1),
                            iconColor: Colors.red,
                          ),

                          // أزرار تحكم الخط
                          Row(
                            children: [
                              _buildControlButton(
                                icon: Icons.fullscreen,
                                onTap: _toggleFullscreen,
                                backgroundColor:
                                    AppColors.getAzkarColor(isDarkMode)
                                        .withOpacity(0.1),
                                iconColor: AppColors.getAzkarColor(isDarkMode),
                              ),
                              const SizedBox(width: 8),
                              _buildControlButton(
                                icon: Icons.content_copy,
                                onTap: _copyToClipboard,
                                backgroundColor: Colors.green.withOpacity(0.1),
                                iconColor: Colors.green,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                // أزرار تحكم الخط السفلية
                if (_showControls)
                  Positioned(
                    bottom: 32,
                    left: 24,
                    right: 24,
                    child: AnimatedOpacity(
                      opacity: _showControls ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 16),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Colors.grey.shade900.withOpacity(0.95)
                              : Colors.white.withOpacity(0.95),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 20,
                              spreadRadius: 5,
                            ),
                          ],
                          border: Border.all(
                            color: AppColors.getAzkarColor(isDarkMode)
                                .withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            // تصغير الخط
                            _buildFontControlButton(
                              icon: Icons.text_decrease,
                              onTap: _decreaseFontSize,
                              enabled: _fontSize > _minFontSize,
                            ),

                            // عرض حجم الخط الحالي
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: AppColors.getAzkarColor(isDarkMode)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${_fontSize.toInt()}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.getAzkarColor(isDarkMode),
                                ),
                              ),
                            ),

                            // تكبير الخط
                            _buildFontControlButton(
                              icon: Icons.text_increase,
                              onTap: _increaseFontSize,
                              enabled: _fontSize < _maxFontSize,
                            ),

                            // إعادة تعيين حجم الخط
                            _buildFontControlButton(
                              icon: Icons.refresh,
                              onTap: _resetFontSize,
                              enabled: _fontSize != 18.0,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // زر الإغلاق في وضع الشاشة الكاملة
                if (_isFullscreen && _showControls)
                  Positioned(
                    top: 16,
                    right: 16,
                    child: AnimatedOpacity(
                      opacity: _showControls ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: _buildControlButton(
                        icon: Icons.fullscreen_exit,
                        onTap: _toggleFullscreen,
                        backgroundColor: AppColors.getAzkarColor(isDarkMode)
                            .withOpacity(0.1),
                        iconColor: AppColors.getAzkarColor(isDarkMode),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildFontControlButton({
    required IconData icon,
    required VoidCallback onTap,
    required bool enabled,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: enabled ? onTap : null,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: enabled
              ? AppColors.getAzkarColor(isDarkMode).withOpacity(0.1)
              : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: enabled
                ? AppColors.getAzkarColor(isDarkMode).withOpacity(0.2)
                : Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: enabled ? AppColors.getAzkarColor(isDarkMode) : Colors.grey,
          size: 20,
        ),
      ),
    );
  }
}
