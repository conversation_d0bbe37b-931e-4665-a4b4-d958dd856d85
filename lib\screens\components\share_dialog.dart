part of '../azkar_details_screen.dart';

class ShareDialog extends StatefulWidget {
  final String title;
  final String content;
  final String? imageUrl;
  final bool isZikr;

  const ShareDialog({
    Key? key,
    required this.title,
    required this.content,
    this.imageUrl,
    this.isZikr = true,
  }) : super(key: key);

  @override
  State<ShareDialog> createState() => _ShareDialogState();
}

class _ShareDialogState extends State<ShareDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _includeTitle = true;
  bool _includeSource = true;
  late bool _includeFadl;
  bool _includeAppName = true;

  @override
  void initState() {
    super.initState();
    _includeFadl = widget.isZikr;
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 8,
        backgroundColor: isDarkMode
            ? const Color(0xFF252A34) /* TasbihColors.darkCardColor */
            : Colors.white,
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: screenSize.width * 0.9,
            maxHeight: screenSize.height * 0.8,
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header - مُحسّن لتجنب مشكلة ParentDataWidget
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      children: [
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                          splashRadius: 20,
                        ),
                        const Spacer(),
                        Text(
                          'مشاركة المحتوى',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.share_rounded,
                          color: AppColors.getAzkarColor(isDarkMode),
                          size: 28,
                        ),
                      ],
                    );
                  },
                ),
                const Divider(height: 24),

                // Preview
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? const Color(0xFF16213E).withAlpha(
                              150) // TasbihColors.darkBackgroundSecondary
                          : Colors.grey[100]!.withAlpha(150),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isDarkMode
                            ? const Color(0xFF252A34)
                                .withAlpha(200) // TasbihColors.darkCardColor
                            : Colors.grey[300]!,
                        width: 1,
                      ),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_includeTitle)
                            Text(
                              widget.title,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color:
                                    isDarkMode ? Colors.white : Colors.black87,
                              ),
                            ),
                          if (_includeTitle) const SizedBox(height: 12),
                          Text(
                            widget.content,
                            style: TextStyle(
                              fontSize: 16,
                              height: 1.5,
                              color: isDarkMode ? Colors.white : Colors.black87,
                            ),
                          ),
                          if (_includeSource && widget.isZikr)
                            const SizedBox(height: 12),
                          if (_includeSource && widget.isZikr)
                            Text(
                              'المصدر: صحيح البخاري',
                              style: TextStyle(
                                fontSize: 14,
                                fontStyle: FontStyle.italic,
                                color: isDarkMode
                                    ? const Color(
                                        0xFFAAAAAA) // TasbihColors.darkTextSecondary
                                    : Colors.grey[700],
                              ),
                            ),
                          if (_includeFadl && widget.isZikr)
                            const SizedBox(height: 12),
                          if (_includeFadl && widget.isZikr)
                            Text(
                              'الفضل: من قالها أعطاه الله من الأجر كذا وكذا',
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? const Color(
                                        0xFFAAAAAA) // TasbihColors.darkTextSecondary
                                    : Colors.grey[700],
                              ),
                            ),
                          if (_includeAppName) const SizedBox(height: 16),
                          if (_includeAppName)
                            Text(
                              'مشاركة من تطبيق وهج السالك',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                                color: AppColors.getAzkarColor(isDarkMode),
                              ),
                              textAlign: TextAlign.start,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Options
                Text(
                  'خيارات المشاركة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),

                // خيارات المشاركة - مُحسّن لتجنب مشكلة ParentDataWidget
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Wrap(
                      spacing: 8,
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      children: [
                        _buildOptionChip(
                          label: 'العنوان',
                          selected: _includeTitle,
                          onSelected: (value) {
                            setState(() {
                              _includeTitle = value;
                            });
                          },
                        ),
                        if (widget.isZikr)
                          _buildOptionChip(
                            label: 'المصدر',
                            selected: _includeSource,
                            onSelected: (value) {
                              setState(() {
                                _includeSource = value;
                              });
                            },
                          ),
                        if (widget.isZikr)
                          _buildOptionChip(
                            label: 'الفضل',
                            selected: _includeFadl,
                            onSelected: (value) {
                              setState(() {
                                _includeFadl = value;
                              });
                            },
                          ),
                        _buildOptionChip(
                          label: 'اسم التطبيق',
                          selected: _includeAppName,
                          onSelected: (value) {
                            setState(() {
                              _includeAppName = value;
                            });
                          },
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 20),

                // Share buttons - مُحسّن لتجنب مشكلة ParentDataWidget
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildShareButton(
                          icon: Icons.content_copy,
                          label: 'نسخ',
                          onTap: _copyToClipboard,
                        ),
                        _buildShareButton(
                          icon: Icons.share,
                          label: 'مشاركة',
                          onTap: _shareContent,
                        ),
                        _buildShareButton(
                          icon: Icons.message,
                          label: 'رسالة',
                          onTap: _shareViaMessage,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOptionChip({
    required String label,
    required bool selected,
    required ValueChanged<bool> onSelected,
  }) {
    return FilterChip(
      label: Text(label),
      selected: selected,
      onSelected: onSelected,
      selectedColor: AppColors.getAzkarColor(
              Theme.of(context).brightness == Brightness.dark)
          .withAlpha(77), // 0.3 * 255 = 77
      checkmarkColor: Colors.white,
      labelStyle: TextStyle(
        color: selected ? Colors.white : null,
        fontWeight: selected ? FontWeight.bold : null,
      ),
      backgroundColor: Colors.grey.withAlpha(26), // 0.1 * 255 = 26
    );
  }

  Widget _buildShareButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.getAzkarColor(
                        Theme.of(context).brightness == Brightness.dark)
                    .withAlpha(51), // 0.2 * 255 = 51
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppColors.getAzkarColor(
                    Theme.of(context).brightness == Brightness.dark),
                size: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getFormattedContent() {
    String result = '';

    if (_includeTitle) {
      result += '${widget.title}\n\n';
    }

    result += widget.content;

    if (_includeSource && widget.isZikr) {
      result += '\n\nالمصدر: صحيح البخاري';
    }

    if (_includeFadl && widget.isZikr) {
      result += '\n\nالفضل: من قالها أعطاه الله من الأجر كذا وكذا';
    }

    if (_includeAppName) {
      result += '\n\nمشاركة من تطبيق وهج السالك';
    }

    return result;
  }

  void _copyToClipboard() {
    final content = _getFormattedContent();
    Clipboard.setData(ClipboardData(text: content)).then((_) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نسخ المحتوى'),
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }
    });
  }

  void _shareContent() {
    final content = _getFormattedContent();
    Share.share(content);
    Navigator.pop(context);
  }

  void _shareViaMessage() {
    final content = _getFormattedContent();
    // Aquí se podría implementar la integración con aplicaciones de mensajería
    // Por ahora, usamos el método general de compartir
    Share.share(content);
    Navigator.pop(context);
  }
}
