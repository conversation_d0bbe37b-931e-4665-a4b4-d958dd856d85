import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/zikr.dart';
import '../utils/app_colors.dart';
import '../screens/azkar_details_screen.dart';

class ZikrItemCard extends StatelessWidget {
  final ZikrItem zikr;
  final int index;
  final bool isFavorite;
  final Function(String) onFavoriteToggle;
  final Function(String, int) onCounterIncrement;
  final Function(String) onCounterReset;
  final Map<String, int>? completedCounts;

  const ZikrItemCard({
    super.key,
    required this.zikr,
    required this.index,
    this.isFavorite = false,
    required this.onFavoriteToggle,
    required this.onCounterIncrement,
    required this.onCounterReset,
    this.completedCounts,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final currentCount =
        completedCounts != null ? completedCounts![zikr.id] ?? 0 : 0;
    final isCompleted = currentCount >= zikr.count;
    final screenSize = MediaQuery.of(context).size;

    // استخدام الألوان المحسنة من ملف الألوان
    final Color textColor = AppColors.getTextColor(isDarkMode);
    final Color cardColor = AppColors.getCardColor(isDarkMode);
    final Color shadowColor = isDarkMode
        ? Colors.black.withAlpha(77) // 0.3 * 255 = 77
        : AppColors.getAzkarColorWithOpacity(isDarkMode, AppColors.opacity20);

    // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
    return RepaintBoundary(
      child: Card(
        // تقليل قيمة الارتفاع لتحسين الأداء
        elevation: 2.5,
        shadowColor: shadowColor,
        color: cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(screenSize.width * 0.04),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildCardHeader(context, isCompleted),
            _buildZikrText(context, textColor),
            if (zikr.fadl != null && zikr.fadl!.isNotEmpty)
              _buildZikrFadl(context),
            if (zikr.source != null && zikr.source!.isNotEmpty)
              _buildZikrSource(context),
            _buildCardFooter(context, currentCount, isCompleted),
          ],
        ),
      ),
    );
  }

  Widget _buildCardHeader(BuildContext context, bool isCompleted) {
    final screenSize = MediaQuery.of(context).size;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: screenSize.width * 0.04,
        vertical: screenSize.height * 0.015,
      ),
      decoration: BoxDecoration(
        color: isCompleted
            ? Colors.green.withAlpha(26) // 0.1 * 255 = 26
            : AppColors.getAzkarColor(
                    Theme.of(context).brightness == Brightness.dark)
                .withAlpha(26), // 0.1 * 255 = 26
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(screenSize.width * 0.04),
          topRight: Radius.circular(screenSize.width * 0.04),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                width: screenSize.width * 0.06,
                height: screenSize.width * 0.06,
                decoration: BoxDecoration(
                  color: isCompleted
                      ? Colors.green
                      : AppColors.getAzkarColor(
                          Theme.of(context).brightness == Brightness.dark),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '$index',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: screenSize.width * 0.03,
                    ),
                  ),
                ),
              ),
              SizedBox(width: screenSize.width * 0.02),
              if (isCompleted)
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: screenSize.width * 0.04,
                    ),
                    SizedBox(width: screenSize.width * 0.01),
                    Text(
                      'مكتمل',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                        fontSize: screenSize.width * 0.03,
                      ),
                    ),
                  ],
                ),
            ],
          ),
          // أزرار الإجراءات - مُحسّن لتجنب مشكلة ParentDataWidget
          RepaintBoundary(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  children: [
                    IconButton(
                      icon: Icon(
                        isFavorite ? Icons.favorite : Icons.favorite_border,
                        color: isFavorite ? Colors.red : Colors.grey,
                      ),
                      onPressed: () {
                        // استخدام Future.microtask لتجنب تحديث الحالة أثناء مرحلة البناء
                        Future.microtask(() => onFavoriteToggle(zikr.id));
                      },
                      tooltip:
                          isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة',
                      iconSize: screenSize.width * 0.05,
                      constraints: BoxConstraints(
                        minWidth: screenSize.width * 0.09,
                        minHeight: screenSize.width * 0.09,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () => _copyZikrText(context),
                      tooltip: 'نسخ',
                      iconSize: screenSize.width * 0.05,
                      constraints: BoxConstraints(
                        minWidth: screenSize.width * 0.09,
                        minHeight: screenSize.width * 0.09,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () => _shareZikr(context),
                      tooltip: 'مشاركة',
                      iconSize: screenSize.width * 0.05,
                      constraints: BoxConstraints(
                        minWidth: screenSize.width * 0.09,
                        minHeight: screenSize.width * 0.09,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildZikrText(BuildContext context, Color textColor) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;
    final bool isLongText = zikr.text.length > 300;
    final Color azkarColor = AppColors.getAzkarColor(isDarkMode);

    return Padding(
      padding: EdgeInsets.all(screenSize.width * 0.04),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // نص الذكر مع إمكانية التمرير
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: isLongText
                  ? screenSize.height * 0.25
                  : screenSize.height * 0.35,
            ),
            child: SingleChildScrollView(
              child: Text(
                zikr.text,
                style: TextStyle(
                  // زيادة حجم الخط قليلاً في الوضع المظلم لتحسين القراءة
                  fontSize: screenSize.width * (isDarkMode ? 0.048 : 0.045),
                  // زيادة المسافة بين السطور في الوضع المظلم
                  height: isDarkMode ? 1.9 : 1.8,
                  color: textColor,
                  // إضافة وزن خط أكبر في الوضع المظلم لتحسين الوضوح
                  fontWeight: isDarkMode ? FontWeight.w500 : FontWeight.normal,
                  // إضافة ظل خفيف للنص في الوضع المظلم لتحسين القراءة
                  shadows: isDarkMode
                      ? [
                          Shadow(
                            color: Colors.black.withAlpha(40),
                            blurRadius: 0.5,
                          )
                        ]
                      : null,
                  letterSpacing: isDarkMode ? 0.2 : 0,
                ),
                textAlign: TextAlign.justify,
                textDirection: TextDirection.rtl,
              ),
            ),
          ),

          // زر "عرض المزيد" إذا كان النص طويلاً - مُحسّن للأداء
          if (isLongText)
            Align(
              alignment: Alignment.center,
              child: Padding(
                padding: const EdgeInsets.only(top: 12.0),
                // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                child: RepaintBoundary(
                  child: InkWell(
                    onTap: () => _showFullZikrText(context),
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: azkarColor.withAlpha(13), // 0.05 * 255 = 13
                        borderRadius: BorderRadius.circular(16),
                        // تبسيط الحدود لتحسين الأداء
                        border: Border.all(
                          color: azkarColor.withAlpha(51), // 0.2 * 255 = 51
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.fullscreen,
                            size: 16,
                            color: azkarColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'عرض النص كاملاً',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: azkarColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildZikrFadl(BuildContext context) {
    final String fadlText = zikr.fadl ?? '';
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;

    // استخدام ألوان محسنة للفضل
    final Color fadlColor = AppColors.getFadlColor(isDarkMode);
    final Color fadlBackgroundColor = isDarkMode
        ? fadlColor.withAlpha(26) // 0.1 * 255 = 26
        : fadlColor.withAlpha(20); // 0.08 * 255 = 20

    // التحقق مما إذا كان النص طويلاً
    final bool isLongText = fadlText.length > 150;

    // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
    return RepaintBoundary(
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          screenSize.width * 0.04,
          0,
          screenSize.width * 0.04,
          screenSize.width * 0.04,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان الفضل مع أيقونة - مُحسّن لتجنب مشكلة ParentDataWidget
            LayoutBuilder(
              builder: (context, constraints) {
                return Row(
                  textDirection: TextDirection
                      .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // زر عرض النص كاملاً إذا كان طويلاً - مُحسّن للأداء
                    if (isLongText)
                      // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                      RepaintBoundary(
                        child: GestureDetector(
                          onTap: () => _showFullFadlText(context),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: fadlColor.withAlpha(13), // 0.05 * 255 = 13
                              borderRadius: BorderRadius.circular(12),
                              // تبسيط الحدود لتحسين الأداء
                              border: Border.all(
                                color:
                                    fadlColor.withAlpha(51), // 0.2 * 255 = 51
                                width: 0.5,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.fullscreen,
                                  size: 12,
                                  color: fadlColor,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'عرض كامل',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: fadlColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    Row(
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          size: 16,
                          color: fadlColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'الفضل:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: fadlColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 4),
            // محتوى الفضل - مُحسّن للأداء
            Container(
              padding: EdgeInsets.all(screenSize.width * 0.03),
              decoration: BoxDecoration(
                color: fadlBackgroundColor,
                borderRadius: BorderRadius.circular(8),
                // تبسيط الحدود لتحسين الأداء
                border: isDarkMode
                    ? Border.all(
                        color: fadlColor.withAlpha(51), // 0.2 * 255 = 51
                        width: 0.5,
                      )
                    : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // نص الفضل مع تقييد الارتفاع - مُحسّن للأداء
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: isLongText
                          ? screenSize.height * 0.12
                          : screenSize.height * 0.15,
                    ),
                    // استخدام RepaintBoundary لتحسين أداء التمرير
                    child: RepaintBoundary(
                      child: SingleChildScrollView(
                        child: Text(
                          fadlText,
                          style: TextStyle(
                            // تبسيط حجم الخط لتحسين الأداء
                            fontSize:
                                screenSize.width * (isDarkMode ? 0.038 : 0.035),
                            color: fadlColor,
                            // تبسيط وزن الخط لتحسين الأداء
                            fontWeight: isDarkMode
                                ? FontWeight.w500
                                : FontWeight.normal,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ),
                  ),

                  // زر عرض المزيد إذا كان النص طويلاً - مُحسّن للأداء
                  if (isLongText)
                    Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                        child: RepaintBoundary(
                          child: InkWell(
                            onTap: () => _showFullFadlText(context),
                            borderRadius: BorderRadius.circular(16),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              // زر عرض المزيد - مُحسّن لتجنب مشكلة ParentDataWidget
                              child: LayoutBuilder(
                                builder: (context, constraints) {
                                  return Row(
                                    textDirection: TextDirection
                                        .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        'عرض المزيد',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          color: fadlColor,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Icon(
                                        Icons.more_horiz,
                                        size: 16,
                                        color: fadlColor,
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // دالة لعرض نص الفضل كاملاً في نافذة منبثقة - مُحسّنة للأداء
  void _showFullFadlText(BuildContext context) {
    final String fadlText = zikr.fadl ?? '';
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color fadlColor = AppColors.getFadlColor(isDarkMode);
    final Color backgroundColor = isDarkMode
        ? const Color(0xFF252A34) // TasbihColors.darkCardColor
        : Colors.white;

    showDialog(
      context: context,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: backgroundColor,
        // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
        child: RepaintBoundary(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // عنوان الحوار - مُحسّن لتجنب مشكلة ParentDataWidget
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.close,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                        Row(
                          children: [
                            Text(
                              'فضل الذكر',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: fadlColor,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.auto_awesome,
                              size: 20,
                              color: fadlColor,
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ),

                const Divider(height: 24),

                // محتوى الفضل في منطقة قابلة للتمرير - مُحسّن للأداء
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  // استخدام RepaintBoundary لتحسين أداء التمرير
                  child: RepaintBoundary(
                    child: SingleChildScrollView(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? fadlColor.withAlpha(13) // 0.05 * 255 = 13
                              : fadlColor.withAlpha(10), // 0.04 * 255 = 10
                          borderRadius: BorderRadius.circular(12),
                          // تبسيط الحدود لتحسين الأداء
                          border: Border.all(
                            color: fadlColor.withAlpha(38), // 0.15 * 255 = 38
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          fadlText,
                          style: TextStyle(
                            fontSize: 16,
                            height: 1.8,
                            color: isDarkMode ? Colors.white : Colors.black87,
                            // تبسيط وزن الخط لتحسين الأداء
                            fontWeight: isDarkMode
                                ? FontWeight.w500
                                : FontWeight.normal,
                          ),
                          textAlign: TextAlign.justify,
                          textDirection: TextDirection.rtl,
                        ),
                      ),
                    ),
                  ),
                ),

                // أزرار الإجراءات - مُحسّنة للأداء
                const SizedBox(height: 20),
                // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                RepaintBoundary(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      TextButton.icon(
                        onPressed: () async {
                          // استخدام async/await بدلاً من then
                          await Clipboard.setData(
                              ClipboardData(text: fadlText));

                          // التحقق من أن السياق لا يزال مرتبطاً
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم نسخ النص'),
                                behavior: SnackBarBehavior.floating,
                                // تقليل مدة الرسوم المتحركة لتحسين الأداء
                                duration: Duration(milliseconds: 800),
                              ),
                            );
                          }
                        },
                        icon: const Icon(Icons.copy),
                        label: const Text('نسخ'),
                        style: TextButton.styleFrom(
                          foregroundColor: fadlColor,
                        ),
                      ),
                      TextButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: const Icon(Icons.check_circle_outline),
                        label: const Text('تم'),
                        style: TextButton.styleFrom(
                          foregroundColor: fadlColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildZikrSource(BuildContext context) {
    final String sourceText = zikr.source ?? '';
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;

    // استخدام ألوان محسنة للمصدر
    final Color sourceColor = AppColors.getSourceColor(isDarkMode);

    // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
    return RepaintBoundary(
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          screenSize.width * 0.04,
          0,
          screenSize.width * 0.04,
          screenSize.width * 0.04,
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: screenSize.width * 0.03,
            vertical: screenSize.width * 0.02,
          ),
          decoration: BoxDecoration(
            color: isDarkMode
                ? sourceColor.withAlpha(20) // 0.08 * 255 = 20
                : sourceColor.withAlpha(13), // 0.05 * 255 = 13
            borderRadius: BorderRadius.circular(8),
            // تبسيط الحدود لتحسين الأداء
            border: isDarkMode
                ? Border.all(
                    color: sourceColor.withAlpha(38), // 0.15 * 255 = 38
                    width: 0.5,
                  )
                : null,
          ),
          child: Row(
            children: [
              Icon(
                Icons.menu_book,
                size: 16,
                color: sourceColor,
              ),
              const SizedBox(width: 8),
              Text(
                'المصدر: ',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: sourceColor,
                  fontSize: isDarkMode ? 14.5 : 14,
                ),
              ),
              Expanded(
                child: Text(
                  sourceText,
                  style: TextStyle(
                    fontSize: isDarkMode ? 14.5 : 14,
                    color: sourceColor,
                    fontWeight:
                        isDarkMode ? FontWeight.w500 : FontWeight.normal,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardFooter(
      BuildContext context, int currentCount, bool isCompleted) {
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
    return RepaintBoundary(
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: screenSize.width * 0.04,
          vertical: screenSize.height * 0.015,
        ),
        decoration: BoxDecoration(
          color: isDarkMode
              ? const Color(0xFF252A34)
                  .withAlpha(77) // TasbihColors.darkCardColor, 0.3 * 255 = 77
              : AppColors.getAzkarColor(isDarkMode)
                  .withAlpha(13), // 0.05 * 255 = 13
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(screenSize.width * 0.04),
            bottomRight: Radius.circular(screenSize.width * 0.04),
          ),
        ),
        // صف التذييل - مُحسّن لتجنب مشكلة ParentDataWidget
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Row(
              textDirection:
                  TextDirection.rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                RepaintBoundary(
                  child: Row(
                    children: [
                      InkWell(
                        onTap: isCompleted
                            ? () => onCounterReset(zikr.id)
                            : () => onCounterIncrement(zikr.id, zikr.count),
                        borderRadius:
                            BorderRadius.circular(screenSize.width * 0.05),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenSize.width * 0.03,
                            vertical: screenSize.height * 0.008,
                          ),
                          decoration: BoxDecoration(
                            color: isCompleted
                                ? Colors.green
                                : AppColors.getAzkarColor(
                                    Theme.of(context).brightness ==
                                        Brightness.dark),
                            borderRadius:
                                BorderRadius.circular(screenSize.width * 0.05),
                          ),
                          child: Text(
                            isCompleted
                                ? 'إعادة التعيين'
                                : '$currentCount / ${zikr.count}',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: screenSize.width * 0.03,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // معلومات عدد التكرار - مُحسّنة للأداء
                Row(
                  children: [
                    Text(
                      'عدد التكرار: ${zikr.count}',
                      style: TextStyle(
                        fontSize: screenSize.width * 0.035,
                        color: isDarkMode
                            ? const Color(
                                0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                            : Colors.grey,
                      ),
                    ),
                    SizedBox(width: screenSize.width * 0.01),
                    Icon(
                      Icons.repeat,
                      size: screenSize.width * 0.04,
                      color: isDarkMode
                          ? const Color(
                              0xFFAAAAAA) /* TasbihColors.darkTextSecondary */
                          : Colors.grey,
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  // دالة نسخ النص - مُحسّنة للأداء
  void _copyZikrText(BuildContext context) async {
    // استخدام async/await بدلاً من then لتحسين الأداء
    await Clipboard.setData(ClipboardData(text: zikr.text));

    // التحقق من أن السياق لا يزال مرتبطاً
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ النص'),
          behavior: SnackBarBehavior.floating,
          // تقليل مدة الرسوم المتحركة لتحسين الأداء
          duration: Duration(milliseconds: 800),
        ),
      );
    }
  }

  // دالة مشاركة الذكر - مُحسّنة للأداء
  void _shareZikr(BuildContext context) {
    // عرض حوار المشاركة المتقدم
    showDialog(
      context: context,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      barrierDismissible: true,
      builder: (context) => ShareDialog(
        title: 'ذكر',
        content: zikr.text,
        isZikr: true,
        source: zikr.source, // تمرير المصدر
        fadl: zikr.fadl, // تمرير الفضل
      ),
    );
  }

  // دالة عرض النص كاملاً - مُحسّنة للأداء
  void _showFullZikrText(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = AppColors.getTextColor(isDarkMode);
    final azkarColor = AppColors.getAzkarColor(isDarkMode);
    final backgroundColor = isDarkMode
        ? const Color(0xFF252A34) // TasbihColors.darkCardColor
        : Colors.white;

    showDialog(
      context: context,
      // تقليل مدة الرسوم المتحركة لتحسين الأداء
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: backgroundColor,
        // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
        child: RepaintBoundary(
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // عنوان الحوار - مُحسّن لتجنب مشكلة ParentDataWidget
                LayoutBuilder(
                  builder: (context, constraints) {
                    return Row(
                      textDirection: TextDirection
                          .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: Icon(
                            Icons.close,
                            color: isDarkMode ? Colors.white70 : Colors.black54,
                          ),
                          onPressed: () => Navigator.of(context).pop(),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                        Row(
                          children: [
                            Text(
                              'نص الذكر',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: azkarColor,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              Icons.format_quote,
                              size: 20,
                              color: azkarColor,
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ),

                const Divider(height: 24),

                // محتوى الذكر في منطقة قابلة للتمرير - مُحسّن للأداء
                ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  // استخدام RepaintBoundary لتحسين أداء التمرير
                  child: RepaintBoundary(
                    child: SingleChildScrollView(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? azkarColor.withAlpha(13) // 0.05 * 255 = 13
                              : azkarColor.withAlpha(10), // 0.04 * 255 = 10
                          borderRadius: BorderRadius.circular(12),
                          // تبسيط الحدود لتحسين الأداء
                          border: Border.all(
                            color: azkarColor.withAlpha(38), // 0.15 * 255 = 38
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          zikr.text,
                          style: TextStyle(
                            fontSize: 16,
                            height: 1.8,
                            color: textColor,
                            // تبسيط وزن الخط لتحسين الأداء
                            fontWeight: isDarkMode
                                ? FontWeight.w500
                                : FontWeight.normal,
                          ),
                          textAlign: TextAlign.justify,
                          textDirection: TextDirection.rtl,
                        ),
                      ),
                    ),
                  ),
                ),

                // أزرار الإجراءات - مُحسّن لتجنب مشكلة ParentDataWidget
                const SizedBox(height: 20),
                // استخدام RepaintBoundary لتحسين أداء الرسوم المتحركة
                RepaintBoundary(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return Row(
                        textDirection: TextDirection
                            .rtl, // تحديد اتجاه الصف من اليمين إلى اليسار
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop();
                              _shareZikr(context);
                            },
                            icon: const Icon(Icons.share),
                            label: const Text('مشاركة'),
                            style: TextButton.styleFrom(
                              foregroundColor: azkarColor,
                            ),
                          ),
                          TextButton.icon(
                            onPressed: () async {
                              // استخدام async/await بدلاً من then
                              await Clipboard.setData(
                                  ClipboardData(text: zikr.text));

                              // التحقق من أن السياق لا يزال مرتبطاً
                              if (context.mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('تم نسخ النص'),
                                    behavior: SnackBarBehavior.floating,
                                    // تقليل مدة الرسوم المتحركة لتحسين الأداء
                                    duration: Duration(milliseconds: 800),
                                  ),
                                );
                              }
                            },
                            icon: const Icon(Icons.copy),
                            label: const Text('نسخ'),
                            style: TextButton.styleFrom(
                              foregroundColor: azkarColor,
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
